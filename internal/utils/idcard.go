package utils

import (
	"fmt"
	"strconv"
	"time"
)

// CalculateAgeFromIDCard 从身份证号码计算年龄
// idCard: 18位身份证号码
// return: 年龄, 错误信息
func CalculateAgeFromIDCard(idCard string) (int, error) {
	// 校验身份证号码长度
	if len(idCard) != 18 {
		return 0, fmt.Errorf("身份证号码格式错误，长度应为18位，实际长度: %d", len(idCard))
	}

	// 从身份证号提取出生日期(YYYYMMDD)，第7位到第15位，索引从0开始则是6到14
	birthDateStr := idCard[6:14]

	// 解析年月日
	year, err := strconv.Atoi(birthDateStr[:4])
	if err != nil {
		return 0, fmt.Errorf("身份证号码中的年份格式错误: %s", birthDateStr[:4])
	}

	month, err := strconv.At<PERSON>(birthDateStr[4:6])
	if err != nil {
		return 0, fmt.Errorf("身份证号码中的月份格式错误: %s", birthDateStr[4:6])
	}

	day, err := strconv.Atoi(birthDateStr[6:])
	if err != nil {
		return 0, fmt.Errorf("身份证号码中的日期格式错误: %s", birthDateStr[6:])
	}

	// 校验日期有效性
	if month < 1 || month > 12 {
		return 0, fmt.Errorf("身份证号码中的月份无效: %d", month)
	}

	if day < 1 || day > 31 {
		return 0, fmt.Errorf("身份证号码中的日期无效: %d", day)
	}

	// 转换为time.Time类型
	birthDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)

	// 计算年龄
	now := time.Now()
	age := now.Year() - birthDate.Year()

	// 如果今年还没过生日，年龄减1
	if now.Month() < birthDate.Month() || (now.Month() == birthDate.Month() && now.Day() < birthDate.Day()) {
		age--
	}

	// 校验年龄合理性（假设年龄应在0-150之间）
	if age < 0 || age > 150 {
		return 0, fmt.Errorf("计算出的年龄不合理: %d", age)
	}

	return age, nil
}
