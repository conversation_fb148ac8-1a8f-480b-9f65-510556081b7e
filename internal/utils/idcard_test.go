package utils

import (
	"testing"
	"time"
)

func TestCalculateAgeFromIDCard(t *testing.T) {
	// 获取当前时间用于计算预期年龄
	now := time.Now()
	
	tests := []struct {
		name        string
		idCard      string
		expectError bool
		expectAge   int
	}{
		{
			name:        "正常身份证号码-1990年出生",
			idCard:      "11010119900101001X",
			expectError: false,
			expectAge:   now.Year() - 1990, // 根据当前年份计算
		},
		{
			name:        "正常身份证号码-2000年出生",
			idCard:      "110101200006150018",
			expectError: false,
			expectAge:   calculateExpectedAge(2000, 6, 15),
		},
		{
			name:        "正常身份证号码-1985年出生",
			idCard:      "110101198512250019",
			expectError: false,
			expectAge:   calculateExpectedAge(1985, 12, 25),
		},
		{
			name:        "身份证号码长度不足",
			idCard:      "1101011990010100",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码长度过长",
			idCard:      "11010119900101001XX",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码年份格式错误",
			idCard:      "110101ABCD0101001X",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码月份格式错误",
			idCard:      "110101199013010018",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码日期格式错误",
			idCard:      "110101199001320018",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码月份无效",
			idCard:      "110101199000010018",
			expectError: true,
			expectAge:   0,
		},
		{
			name:        "身份证号码日期无效",
			idCard:      "110101199001000018",
			expectError: true,
			expectAge:   0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			age, err := CalculateAgeFromIDCard(tt.idCard)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("期望有错误，但没有错误")
				}
			} else {
				if err != nil {
					t.Errorf("不期望有错误，但得到错误: %v", err)
				}
				if age != tt.expectAge {
					t.Errorf("期望年龄 %d，但得到 %d", tt.expectAge, age)
				}
			}
		})
	}
}

// calculateExpectedAge 计算预期年龄的辅助函数
func calculateExpectedAge(year, month, day int) int {
	now := time.Now()
	birthDate := time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.Local)
	
	age := now.Year() - birthDate.Year()
	if now.Month() < birthDate.Month() || (now.Month() == birthDate.Month() && now.Day() < birthDate.Day()) {
		age--
	}
	
	return age
}

func TestCalculateAgeFromIDCard_EdgeCases(t *testing.T) {
	// 测试边界情况
	tests := []struct {
		name        string
		idCard      string
		expectError bool
		description string
	}{
		{
			name:        "今天生日",
			idCard:      generateTodayBirthdayIDCard(),
			expectError: false,
			description: "测试今天正好是生日的情况",
		},
		{
			name:        "明天生日",
			idCard:      generateTomorrowBirthdayIDCard(),
			expectError: false,
			description: "测试明天是生日的情况",
		},
		{
			name:        "昨天生日",
			idCard:      generateYesterdayBirthdayIDCard(),
			expectError: false,
			description: "测试昨天是生日的情况",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			age, err := CalculateAgeFromIDCard(tt.idCard)
			
			if tt.expectError && err == nil {
				t.Errorf("期望有错误，但没有错误")
			}
			if !tt.expectError && err != nil {
				t.Errorf("不期望有错误，但得到错误: %v", err)
			}
			
			// 对于边界情况，我们主要确保不会出错，年龄应该是合理的
			if !tt.expectError && (age < 0 || age > 150) {
				t.Errorf("年龄不合理: %d", age)
			}
		})
	}
}

// 生成今天生日的身份证号码
func generateTodayBirthdayIDCard() string {
	now := time.Now()
	// 假设20年前出生
	year := now.Year() - 20
	return formatIDCard(year, int(now.Month()), now.Day())
}

// 生成明天生日的身份证号码
func generateTomorrowBirthdayIDCard() string {
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	// 假设20年前出生
	year := tomorrow.Year() - 20
	return formatIDCard(year, int(tomorrow.Month()), tomorrow.Day())
}

// 生成昨天生日的身份证号码
func generateYesterdayBirthdayIDCard() string {
	now := time.Now()
	yesterday := now.AddDate(0, 0, -1)
	// 假设20年前出生
	year := yesterday.Year() - 20
	return formatIDCard(year, int(yesterday.Month()), yesterday.Day())
}

// 格式化身份证号码
func formatIDCard(year, month, day int) string {
	return "110101" + 
		   padZero(year, 4) + 
		   padZero(month, 2) + 
		   padZero(day, 2) + 
		   "001X"
}

// 补零函数
func padZero(num, width int) string {
	str := ""
	for i := 0; i < width; i++ {
		str = "0" + str
	}
	numStr := ""
	temp := num
	if temp == 0 {
		numStr = "0"
	} else {
		for temp > 0 {
			numStr = string(rune('0'+temp%10)) + numStr
			temp /= 10
		}
	}
	
	if len(numStr) >= width {
		return numStr
	}
	return str[:width-len(numStr)] + numStr
}
